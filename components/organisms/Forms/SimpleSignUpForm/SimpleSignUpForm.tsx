'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';

import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { Mail, Lock, User } from 'lucide-react';

import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { InputWithIcon } from '@/components/molecules/InputWithIcon/InputWithIcon';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { signUpAction } from '@/actions/user.action';
import { cn } from '@/utils/cn';

import { simpleSignUpSchema, SimpleSignUpFormData } from './SimpleSignUpForm.schema';
import { useRouter } from 'next/navigation';

export default function SimpleSignUpForm() {
  const router = useRouter();
  const [isRegistering, setIsRegistering] = useState(false);
  const [isSigningIn, setIsSigningIn] = useState(false);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(null);
  const [signInError, setSignInError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SimpleSignUpFormData>({
    resolver: zodResolver(simpleSignUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const isLoading = isRegistering || isSigningIn;



  const getButtonText = () => {
    if (isRegistering) {
      return 'Creating Account...';
    }
    if (registrationSuccess && !isSigningIn) {
      return 'Account Created! Signing In...';
    }
    if (isSigningIn) {
      return 'Signing You In...';
    }
    return 'Create Account';
  };



  const onSubmit = async (data: SimpleSignUpFormData) => {
    try {
      // Clear previous states
      setRegistrationError(null);
      setSignInError(null);
      setRegistrationSuccess(false);

      // Step 1: Call server action for registration
      setIsRegistering(true);

      // Create FormData for server action
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('email', data.email);
      formData.append('password', data.password);

      let registrationResponse;
      try {
        registrationResponse = await signUpAction(formData);
      } catch (actionError: any) {
        console.error('Error calling signUpAction:', actionError);
        setIsRegistering(false);
        setRegistrationError('Registration service unavailable. Please try again later.');
        return;
      }
      
      setIsRegistering(false);

      if (registrationResponse.status === 'error') {
        setRegistrationError(typeof registrationResponse.message === 'string'
          ? registrationResponse.message
          : 'Registration failed. Please try again.');
        return;
      }

      // Registration successful - show success state briefly
      setRegistrationSuccess(true);

      // Small delay to show success message
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Sign in with NextAuth after successful registration
      setIsSigningIn(true);

      console.log('🔄 Attempting NextAuth sign-in after registration...');
      let signInResult;
      try {
        signInResult = await signIn('credentials', {
        email: data.email,
        password: data.password,
        redirect: false,
        callbackUrl: '/', // Explicitly set callback URL
      });

      console.log('🔍 NextAuth sign-in result:', signInResult);
      setIsSigningIn(false);

      if (signInResult && !signInResult.error) {
        console.log('✅ NextAuth sign-in successful! Account is ready to use.');
        // Clear any previous errors - user is now signed in successfully
        setSignInError(null);
        // redirect to home page and let the role-based redirection in app/page.tsx handle it
        router.push('/');
      } else {
        console.error('❌ NextAuth sign-in failed:', signInResult?.error);
        // Handle sign-in errors - account was created but sign-in failed
        setSignInError(
          `Account created successfully, but automatic sign-in failed. ${signInResult?.error || 'Please try signing in manually using the sign-in page.'}`
        );
      }
      } catch (signInError: any) {
        console.error('Error during sign-in process:', signInError);
        setIsSigningIn(false);
        setSignInError('Unable to sign in automatically. Please try signing in manually using the sign-in page.');
      }

    } catch (error: any) {
      console.error('Sign-up process error:', error);
      // Determine which phase the error occurred in
      if (isRegistering) {
        setRegistrationError('An unexpected error occurred during registration. Please try again.');
        setIsRegistering(false);
      } else if (isSigningIn) {
        setSignInError('An unexpected error occurred during sign-in. Please try again.');
        setIsSigningIn(false);
      } else {
        setRegistrationError('An unexpected error occurred during the sign-up process.');
      }
    }
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="space-y-6">
          {/* Registration Success Alert */}
          {registrationSuccess && !signInError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="success"
                message="Account created successfully! Signing you in..."
              />
            </motion.div>
          )}

          {/* Registration Error Alert */}
          {registrationError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={registrationError}
              />
            </motion.div>
          )}

          {/* Sign-in Error Alert */}
          {signInError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AlertMessage
                type="error"
                message={signInError}
              />
            </motion.div>
          )}

          {/* Main Sign Up Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="relative">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 rounded-2xl"></div>
              
              {/* Glass Effect */}
              <div className="relative bg-white/70 backdrop-blur-sm border border-white/40 rounded-2xl p-8 shadow-lg">
                {/* Header */}
                <div className="text-center mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                  >
                    <h1 className="text-3xl font-bold text-gray-800 mb-2">
                      Create Your Account
                    </h1>
                    <p className="text-gray-600 mb-2">
                      Transform your teaching with AI-powered worksheets
                    </p>
                    <p className="text-sm text-gray-500">
                      We&apos;ll create your account and sign you in automatically
                    </p>
                  </motion.div>
                </div>

                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" role="form" aria-label="Sign up form">
                  {/* Name Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <FormField
                      label="Full Name"
                      error={errors.name?.message}
                      required
                    >
                      <InputWithIcon
                        type="text"
                        placeholder="Enter your full name"
                        leftIcon={<User size={18} className="text-blue-500" />}
                        className={cn(
                          'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                          'bg-white/50 backdrop-blur-sm',
                          errors.name
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-blue-500'
                        )}
                        {...register('name')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Email Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <FormField
                      label="Email Address"
                      error={errors.email?.message}
                      required
                    >
                      <InputWithIcon
                        type="email"
                        placeholder="Enter your email address"
                        leftIcon={<Mail size={18} className="text-blue-500" />}
                        className={cn(
                          'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                          'bg-white/50 backdrop-blur-sm',
                          errors.email
                            ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                            : 'border-gray-200 focus:border-blue-500'
                        )}
                        {...register('email')}
                      />
                    </FormField>
                  </motion.div>

                  {/* Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <FormField
                      label="Password"
                      error={errors.password?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={18} className="text-blue-500" />
                        </div>
                        <PasswordInput
                          placeholder="Enter your password (min. 8 characters)"
                          hasLeftIcon={true}
                          className={cn(
                            'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                            'bg-white/50 backdrop-blur-sm',
                            errors.password
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-blue-500'
                          )}
                          {...register('password')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Confirm Password Field */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.65 }}
                  >
                    <FormField
                      label="Confirm Password"
                      error={errors.confirmPassword?.message}
                      required
                    >
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                          <Lock size={18} className="text-blue-500" />
                        </div>
                        <PasswordInput
                          placeholder="Confirm your password"
                          hasLeftIcon={true}
                          className={cn(
                            'h-12 text-base border-2 rounded-xl transition-all duration-300 focus:ring-4 focus:ring-blue-100 hover:border-blue-300',
                            'bg-white/50 backdrop-blur-sm',
                            errors.confirmPassword
                              ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                              : 'border-gray-200 focus:border-blue-500'
                          )}
                          {...register('confirmPassword')}
                        />
                      </div>
                    </FormField>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.75 }}
                  >
                    <Button
                      type="submit"
                      variant="primary"
                      className="w-full h-12 text-base font-semibold rounded-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-xl focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 border-0"
                      isLoading={isLoading}
                      disabled={isLoading}
                      aria-label={getButtonText()}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-2">
                          <span className="loading loading-spinner loading-sm"></span>
                          <span>{getButtonText()}</span>
                        </div>
                      ) : (
                        'Create Account'
                      )}
                    </Button>
                  </motion.div>
                </form>
              </div>
            </div>
          </motion.div>

          {/* Navigation to Sign In */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.85 }}
          >
            <div className="text-center pt-6 border-t border-gray-200">
              <p className="text-gray-600">
                Already have an account?{' '}
                <Link
                  href="/auth/sign-in"
                  className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  Sign in here
                </Link>
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
