'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { getMySchool } from '@/actions/school.action';
import { ISchoolResponse } from '@/apis/schoolApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { IntegratedSchoolSetup } from '@/components/organisms/IntegratedSchoolSetup/IntegratedSchoolSetup';

export const MySchoolDashboard: React.FC = () => {
  const { data: session, status: sessionStatus } = useSession();
  const [schoolData, setSchoolData] = useState<ISchoolResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch school data on component mount
  useEffect(() => {
    const fetchSchoolData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // First check if we have school data in session
        if (session?.user?.school && session?.user?.schoolId) {
          // Convert session school data to ISchoolResponse format
          const sessionSchoolData: ISchoolResponse = {
            id: session.user.schoolId,
            name: session.user.school.name,
            address: session.user.school.address,
            phoneNumber: session.user.school.phoneNumber,
            registeredNumber: session.user.school.registeredNumber,
            email: session.user.school.email,
            brand: session.user.school.brand,
          };
          setSchoolData(sessionSchoolData);
          setIsLoading(false);
          return;
        }

        // If no school in session but user has schoolId, fetch from API
        if (session?.user?.schoolId) {
          const response: TTransformResponse<ISchoolResponse | null> = await getMySchool();

          if (response.status === 'success') {
            setSchoolData(response.data);
          } else {
            const errorMsg = Array.isArray(response.message)
              ? response.message.map((m: any) =>
                  typeof m === 'object' && m.field && m.constraints
                    ? `${m.field}: ${m.constraints}`
                    : String(m)
                ).join(', ')
              : String(response.message || 'Failed to fetch school data');
            setError(errorMsg);
          }
        } else {
          // User has no school
          setSchoolData(null);
        }
      } catch (err: any) {
        console.error('Error fetching school data:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    // Only fetch when session is loaded
    if (sessionStatus !== 'loading') {
      fetchSchoolData();
    }
  }, [session, sessionStatus]);

  // Loading state
  if (sessionStatus === 'loading' || isLoading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* School Info Card Skeleton */}
          <div className="lg:col-span-2">
            <div className="card bg-base-100 shadow-md">
              <div className="card-body">
                <div className="skeleton h-6 w-3/4 mb-4"></div>
                <div className="space-y-3">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-5/6"></div>
                  <div className="skeleton h-4 w-4/6"></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Stats Cards Skeleton */}
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="card bg-base-100 shadow-md">
                <div className="card-body">
                  <div className="skeleton h-4 w-1/2 mb-2"></div>
                  <div className="skeleton h-8 w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </div>
      </div>
    );
  }

  // Empty state - no school data, show integrated setup
  if (!schoolData) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <IntegratedSchoolSetup
          onSuccess={() => {
            // Refresh school data after successful creation
            // Use a more gentle refresh approach
            setTimeout(() => {
              window.location.reload();
            }, 1500);
          }}
          onError={(error) => {
            setError(error);
          }}
        />
      </div>
    );
  }

  // Main dashboard with school data - show edit form
  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <IntegratedSchoolSetup
        mode="edit"
        initialData={{
          name: schoolData.name,
          address: schoolData.address || '',
          phoneNumber: schoolData.phoneNumber || '',
          email: schoolData.email || '',
          registeredNumber: schoolData.registeredNumber || '',
          brand: schoolData.brand ? {
            id: schoolData.brand.id,
            logo: schoolData.brand.logo || '',
            color: schoolData.brand.color || '',
            image: schoolData.brand.image || '',
          } : undefined,
        }}
        onSuccess={() => {
          // Refresh school data after successful update
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }}
        onError={(error) => {
          setError(error);
        }}
      />
    </div>
  );
};

export default MySchoolDashboard;
